import { useEffect, useState, useMemo, useRef } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { saveFile } from "../../../../utilities/utils";

interface CalendarTemplateQuery {
    sectionid?: string;
    category?: number;
    docid?: string;
    calendarCommitments?: string;
    deadlineType?: string;
    deadlineCategory?: string;
    calendarPerson?: string;
    calendarGroup?: string;
    calendarReferent?: string;
    praticaSelectUniqueid?: string;
    authority?: string;
    authoritySearch?: string;
    calendarEvasa?: string;
    calendarNonevadere?: string;
    viewName?: string;
    visStart?: number;
    visEnd?: number;
}

export const usePrintCalendarWithTemplate = ({
    query,
    templateId,
    print,
}: {
    query: CalendarTemplateQuery;
    templateId: string;
    print: boolean;
}) => {
    const { doFetch, hasLoaded, loading } = useGetCustom(
        "/calendar/print-model?noTemplateVars=true",
        {},
        null,
        true 
    );

    const [isDownloading, setIsDownloading] = useState(false);
    const downloadInitiatedRef = useRef<string>("");

    const memoizedQuery = useMemo(() => query, [
        query.sectionid,
        query.category,
        query.docid,
        query.calendarCommitments,
        query.deadlineType,
        query.deadlineCategory,
        query.calendarPerson,
        query.calendarGroup,
        query.calendarReferent,
        query.praticaSelectUniqueid,
        query.authority,
        query.authoritySearch,
        query.calendarEvasa,
        query.calendarNonevadere,
        query.viewName,
        query.visStart,
        query.visEnd,
    ]);

    useEffect(() => {
        const downloadKey = `${print}-${templateId}-${JSON.stringify(memoizedQuery)}`;

        if (print && templateId && !isDownloading && downloadInitiatedRef.current !== downloadKey) {
            downloadInitiatedRef.current = downloadKey;
            setIsDownloading(true);

            const printQuery = {
                ...memoizedQuery,
                docid: templateId,
            };

            const downloadFile = async () => {
                try {
                    const response: any = await doFetch(true, printQuery);

                    if (response && response.data) {
                        saveFile({
                            data: response.data,
                            fileName: "Calendario_template",
                            type: "rtf"
                        });
                    }
                } catch (error) {
                    console.error("Error downloading calendar template:", error);
                } finally {
                    setIsDownloading(false);
                }
            };

            downloadFile();
        }

        if (!print) {
            downloadInitiatedRef.current = "";
        }
    }, [print, templateId, memoizedQuery, doFetch]);

    return { hasLoaded, loading, isDownloading };
};
